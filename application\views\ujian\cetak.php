<?php
// استخدام ملف قاعدة البيانات الموحد
require_once 'db.php';

// استخدام الاتصال الموحد
$conn = getMySQLiConnection();
$conn->set_charset("utf8");
?>

<div class="row">
    <!-- Student Info Cards -->
    <div class="col-sm-3">
        <div class="alert bg-green">
            <h4>الطالب<i class="pull-right fa fa-user"></i></h4>
            <span class="d-block"><?= isset($mhs) && $mhs ? $mhs->nama : 'غير محدد' ?></span>
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-blue">
            <h4>الفصل<i class="pull-right fa fa-building-o"></i></h4>
            <span class="d-block"><?= isset($mhs) && $mhs ? $mhs->nama_kelas : 'غير محدد' ?></span>
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-yellow">
            <h4>القسم<i class="pull-right fa fa-graduation-cap"></i></h4>
            <span class="d-block"><?= isset($mhs) && $mhs ? $mhs->nama_jurusan : 'غير محدد' ?></span>
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-red">
            <h4>التاريخ<i class="pull-right fa fa-calendar"></i></h4>
            <span class="d-block"><?= date('l, d M Y') ?></span>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title"><?= $subjudul ?></h3>
                <div class="box-tools pull-right">
                    <a href="<?= base_url('ujian/list') ?>" class="btn btn-sm btn-default">
                        <i class="fa fa-arrow-left"></i> العودة لقائمة الامتحانات
                    </a>
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <!-- Exam Results Table -->
                <div class="row">
                    <div class="col-md-6">
                        <h4><i class="fa fa-info-circle text-blue"></i> معلومات الامتحان</h4>
                        <table class="table table-bordered table-striped">
                            <tr>
                                <th style="width: 40%">اسم الامتحان</th>
                                <td><?= isset($ujian) && $ujian ? $ujian->nama_ujian : 'غير محدد' ?></td>
                            </tr>
                            <tr>
                                <th>المقرر</th>
                                <td><?= isset($ujian) && $ujian ? $ujian->nama_matkul : 'غير محدد' ?></td>
                            </tr>
                            <tr>
                                <th>المعلم</th>
                                <td><?= isset($ujian) && $ujian ? $ujian->nama_dosen : 'غير محدد' ?></td>
                            </tr>
                            <tr>
                                <th>عدد الأسئلة</th>
                                <td><?= isset($ujian) && $ujian ? $ujian->jumlah_soal : 'غير محدد' ?></td>
                            </tr>
                            <tr>
                                <th>الوقت المحدد</th>
                                <td><?= isset($ujian) && $ujian ? $ujian->waktu : 'غير محدد' ?> دقيقة</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h4><i class="fa fa-trophy text-yellow"></i> نتائج الطالب</h4>
                        <table class="table table-bordered table-striped">
                            <tr>
                                <th style="width: 40%">الدرجة الكلية</th>
                                <td><?= isset($hasil) && $hasil ? $hasil->jml_benar : 'غير محدد' ?></td>
                            </tr>
                            <tr>
                                <th>درجة الطالب</th>
                                <td><span class="badge bg-blue"><?= isset($hasil) && $hasil ? $hasil->nilai : 'غير محدد' ?></span></td>
                            </tr>
                            <tr>
                                <th>حالة الطالب</th>
                                <td>
                                    <?php 
                                    if (isset($hasil) && $hasil) {
                                        $status = ($hasil->jml_benar == 0 || $hasil->nilai < ($hasil->jml_benar * 0.5)) ? "راسب" : "ناجح";
                                        $badge_class = ($status == "ناجح") ? "bg-green" : "bg-red";
                                        echo '<span class="badge ' . $badge_class . '">' . $status . '</span>';
                                    } else {
                                        echo 'غير محدد';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <th>تقدير الطالب</th>
                                <td>
                                    <?php 
                                    if (isset($hasil) && $hasil) {
                                        $percentage = ($hasil->nilai / max(1, $hasil->jml_benar)) * 100;
                                        $grade = "";
                                        $badge_class = "";
                                        
                                        if ($percentage >= 85) {
                                            $grade = "امتياز";
                                            $badge_class = "bg-purple";
                                        } elseif ($percentage >= 75) {
                                            $grade = "جيد جداً";
                                            $badge_class = "bg-green";
                                        } elseif ($percentage >= 65) {
                                            $grade = "جيد";
                                            $badge_class = "bg-blue";
                                        } elseif ($percentage >= 50) {
                                            $grade = "مقبول";
                                            $badge_class = "bg-yellow";
                                        } else {
                                            $grade = "راسب";
                                            $badge_class = "bg-red";
                                        }
                                        
                                        echo '<span class="badge ' . $badge_class . '">' . $grade . '</span>';
                                        echo ' <small>(' . number_format($percentage, 1) . '%)</small>';
                                    } else {
                                        echo 'غير محدد';
                                    }
                                    ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="row">
                    <div class="col-sm-12 text-center">
                        <button type="button" class="btn btn-primary" onclick="showQuestions()">
                            <i class="fa fa-list"></i> عرض الأسئلة والإجابات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Questions and Answers Section -->
<div class="row" id="questions-section" style="display: none;">
    <div class="col-sm-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-question-circle"></i> الأسئلة والإجابات</h3>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" onclick="hideQuestions()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <?php
                try {
                    // استخدام الاتصال الموحد
                    $pdo = getPDOConnection();

                    // استعلام لاسترداد السؤال وإجابة الطالب
                    if (isset($hasil) && isset($ujian) && $hasil && $ujian) {
                        $query = "SELECT * FROM h_ujian WHERE mahasiswa_id = ? AND ujian_id = ?";
                        $stmt = $pdo->prepare($query);
                        $stmt->execute([$hasil->mahasiswa_id, $ujian->id_ujian]);
                        
                        $row_number = 1;
                        // عرض السؤال وإجابة الطالب
                        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                            $list_jawaban = explode(',', $row['list_jawaban']);
                            foreach($list_jawaban as $jawaban) {
                                $pieces = explode(':', $jawaban);
                                // استعلام لاسترداد السؤال المطابق للقيمة في $pieces[0]
                                $query_soal = "SELECT * FROM tb_soal WHERE id_soal = ?";
                                $stmt_soal = $pdo->prepare($query_soal);
                                $stmt_soal->execute([$pieces[0]]);
                                $tb_soal = $stmt_soal->fetch(PDO::FETCH_ASSOC);

                                if ($tb_soal) {
                                    echo '<div class="question-block panel panel-default">';
                                    echo '<div class="panel-heading">';
                                    echo '<h4 class="panel-title"><strong>' . $row_number++ . ') ' . $tb_soal['soal'] . '</strong></h4>';
                                    echo '</div>';
                                    echo '<div class="panel-body">';
                                    
                                    // عرض الاختيارات
                                    $options = ['A', 'B', 'C', 'D'];
                                    foreach ($options as $option) {
                                        $option_value = $tb_soal['opsi_' . $option];
                                        $option_file = $tb_soal['file_' . $option];
                                        $correct_answer = $tb_soal['jawaban'];
                                        $student_answer = $pieces[1];

                                        $is_student_answer = ($student_answer == $option);
                                        $is_correct_answer = ($correct_answer == $option);
                                        $is_correct = $is_student_answer && $is_correct_answer;

                                        $class = 'list-group-item';
                                        if ($is_student_answer && $is_correct_answer) {
                                            $class .= ' list-group-item-success';
                                        } elseif ($is_student_answer && !$is_correct_answer) {
                                            $class .= ' list-group-item-danger';
                                        } elseif ($is_correct_answer) {
                                            $class .= ' list-group-item-info';
                                        }

                                        echo '<div class="' . $class . '">';
                                        echo '<span class="badge">' . $option . '</span> ' . $option_value;
                                        
                                        if ($option_file) {
                                            echo '<br><img src="' . base_url('uploads/bank_soal/' . $option_file) . '" class="img-responsive" style="max-width: 200px; margin-top: 10px;" />';
                                        }
                                        
                                        if ($is_student_answer) {
                                            echo $is_correct ? '<span class="pull-right text-success"><i class="fa fa-check"></i> إجابتك صحيحة</span>' : '<span class="pull-right text-danger"><i class="fa fa-times"></i> إجابتك خطأ</span>';
                                        }
                                        
                                        if ($is_correct_answer && !$is_student_answer) {
                                            echo '<span class="pull-right text-info"><i class="fa fa-lightbulb-o"></i> الإجابة الصحيحة</span>';
                                        }
                                        
                                        echo '</div>';
                                    }
                                    
                                    echo '</div>';
                                    echo '</div>';
                                }
                            }
                        }
                    } else {
                        echo '<div class="alert alert-warning"><i class="fa fa-warning"></i> لا توجد بيانات متاحة لعرض الأسئلة والإجابات.</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> حدث خطأ في تحميل البيانات: ' . $e->getMessage() . '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<script>
function showQuestions() {
    document.getElementById('questions-section').style.display = 'block';
    document.querySelector('html').scrollTop = document.getElementById('questions-section').offsetTop;
}

function hideQuestions() {
    document.getElementById('questions-section').style.display = 'none';
}
</script>

<style>
.question-block {
    margin-bottom: 20px;
}

.question-block .panel-heading {
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.question-block .list-group-item {
    border-left: 4px solid transparent;
}

.question-block .list-group-item-success {
    border-left-color: #5cb85c;
    background-color: #dff0d8;
}

.question-block .list-group-item-danger {
    border-left-color: #d9534f;
    background-color: #f2dede;
}

.question-block .list-group-item-info {
    border-left-color: #5bc0de;
    background-color: #d9edf7;
}

@media print {
    .box-tools, .btn-group, #questions-section {
        display: none !important;
    }
    
    .box {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
</style>
