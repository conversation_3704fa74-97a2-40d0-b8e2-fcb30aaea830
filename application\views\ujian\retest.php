<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اخذ الامتحان</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .custom-btn {
            display: inline-block;
            padding: 8px 14px;
            background-color: #fff;
            color: black; /* تغيير لون النص إلى الأسود */
            border: 2px solid #4CAF50;
            border-radius: 5px;
            text-align: center;
            text-decoration: none;
            font-size: 10px;
            cursor: pointer;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        .custom-btn:hover {
            background-color: #45a049;
            border-color: #45a049;
        }
        .custom-btn i {
            margin-left: 5px;
        }

        /* تنسيق زر الرجوع */
        .btn-back {
            display: inline-block;
            padding: 8px 14px;
            background-color: #6c757d;
            color: white;
            border: 2px solid #6c757d;
            border-radius: 5px;
            text-align: center;
            text-decoration: none;
            font-size: 10px;
            cursor: pointer;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
            margin-left: 10px;
            transition: all 0.3s ease;
        }

        .btn-back:hover {
            background-color: #5a6268;
            border-color: #545b62;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
            text-decoration: none;
        }

        .btn-back i {
            margin-right: 5px;
        }
        .custom-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300%;
            height: 300%;
            background-color: #fff;
            border-radius: 50%;
            transition: all 0.3s ease;
            z-index: 0;
            transform: translate(-50%, -50%);
        }
        .custom-btn:hover::before {
            width: 0;
            height: 0;
        }
        .custom-btn span {
            position: relative;
            z-index: 1;
        }
    </style>
</head>
<body>
<div class="callout callout-info">
    <h4>تنبيهات الامتحان</h4>

<?php
// استخدام ملف قاعدة البيانات الموحد
require_once 'db.php';

// إنشاء اتصال بقاعدة البيانات
$conn = getMySQLiConnection();

// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// الحصول على بيانات الطالب
$mahasiswa_nim = $conn->real_escape_string($mhs->nim);
$mahasiswa_nam = $conn->real_escape_string($mhs->nama);

// الحصول على User Agent
$user_agent = $_SERVER['HTTP_USER_AGENT'];

// استخراج اسم الجهاز من الـ user agent باستخدام تعبيرات منتظمة
function extractDeviceName($user_agent) {
    if (preg_match('/\((.*?)\)/', $user_agent, $matches)) {
        return $matches[1];
    }
    return 'Unknown Device';
}

$device_name = extractDeviceName($user_agent);

// التحقق من وجود الجهاز بالفعل في قاعدة البيانات بناءً على device_id
$sql_check_device_id = "SELECT * FROM devices WHERE device_id = '$mahasiswa_nim'";
$result_device_id = $conn->query($sql_check_device_id);

if ($result_device_id->num_rows > 0) {
    // الجهاز موجود بالفعل، التحقق من البيانات
    $row_device = $result_device_id->fetch_assoc();
    if ($row_device['user_agent'] != $device_name) {
        // توجيه إلى صفحة warning.php
        header("Location: warning.php");
        exit();
    }
} else {
    // الجهاز غير موجود، إضافة البيانات الجديدة
    $sql_insert = "INSERT INTO devices (name, device_id, user_agent) VALUES ('$mahasiswa_nam','$mahasiswa_nim', '$device_name')";
    $conn->query($sql_insert);
}

// إغلاق الاتصال
$conn->close();
?>

    <?php
// تعيين قيمة ujian_id
$ujian_id = isset($ujian) && $ujian ? $ujian->id_ujian : 0;

// اتصال بقاعدة البيانات
$conn = getMySQLiConnection();

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// استعلام لجلب قيمة adp من جدول adps
$adp_adps_query = "SELECT adp FROM adps WHERE ujian_id = ? AND nim = ?";
$stmt_adps = $conn->prepare($adp_adps_query);
if ($stmt_adps) {
    $stmt_adps->bind_param("is", $ujian_id, $mahasiswa_nim);
    $stmt_adps->execute();
    $stmt_adps->store_result();
} else {
    echo "<script>alert('خطأ في تحضير الاستعلام: " . $conn->error . "');</script>";
}

// استعلام لجلب قيمة adp من جدول m_ujian
$adp_m_ujian_query = "SELECT adp FROM m_ujian WHERE id_ujian = ?";
$stmt_m_ujian = $conn->prepare($adp_m_ujian_query);
if ($stmt_m_ujian) {
    $stmt_m_ujian->bind_param("i", $ujian_id);
    $stmt_m_ujian->execute();
    $stmt_m_ujian->store_result();
} else {
    echo "<script>alert('خطأ في تحضير الاستعلام: " . $conn->error . "');</script>";
}

// تحقق من وجود قيمة adp في جدول adps وجلبها
if ($stmt_adps && $stmt_adps->num_rows > 0) {
    $stmt_adps->bind_result($adp_adps);
    $stmt_adps->fetch();
} else {
    $adp_adps = 0; // قيمة افتراضية في حالة عدم وجود القيمة
}

// تحقق من وجود قيمة adp في جدول m_ujian وجلبها
if ($stmt_m_ujian->num_rows > 0) {
    $stmt_m_ujian->bind_result($adp_m_ujian);
    $stmt_m_ujian->fetch();

    // حساب الفرق بين adp لجدول adps و adp لجدول m_ujian
    $remaining_attempts = $adp_m_ujian - $adp_adps;

    // عرض تنبيه معلومات المحاولات
    if ($remaining_attempts > 0) {
        echo '<div class="alert alert-success" style="margin-bottom: 15px;">
                <i class="fa fa-info-circle"></i>
                <strong>معلومات المحاولات:</strong><br>
                • عدد المحاولات الكلية: <span class="badge bg-blue">' . $adp_m_ujian . '</span><br>
                • المحاولات المستخدمة: <span class="badge bg-orange">' . $adp_adps . '</span><br>
                • المحاولات المتبقية: <span class="badge bg-green">' . $remaining_attempts . '</span>
              </div>';
    } else {
        echo '<div class="alert alert-danger" style="margin-bottom: 15px;">
                <i class="fa fa-exclamation-triangle"></i>
                <strong>تحذير:</strong> لقد استنفدت جميع المحاولات المسموحة لهذا الامتحان!<br>
                • عدد المحاولات الكلية: <span class="badge bg-blue">' . $adp_m_ujian . '</span><br>
                • المحاولات المستخدمة: <span class="badge bg-red">' . $adp_adps . '</span>
              </div>';
    }
}

// إغلاق الاستعلامات والاتصال بقاعدة البيانات
$stmt_adps->close();
$stmt_m_ujian->close();
$conn->close();
?>


    
</div>
<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">Confirm Data</h3>
    </div>
    <div class="box-body">
        <div class="row">
            <div class="col-sm-6">
                <table class="table table-bordered">
                    <tr>
                        <th>الاسم</th>
                        <td><?= isset($mhs) && $mhs ? $mhs->nama : 'غير محدد' ?></td>
                    </tr>
                    <tr>
                        <th>المعلم</th>
                        <td><?= isset($ujian) && $ujian ? $ujian->nama_dosen : 'غير محدد' ?></td>
                    </tr>
                    <tr>
                        <th>الفصل\القسم</th>
                        <td><?= isset($mhs) && $mhs ? $mhs->nama_kelas . ' / ' . $mhs->nama_jurusan : 'غير محدد' ?></td>
                    </tr>
                    <tr>
                        <th>اسم الامتحان</th>
                        <td><?= isset($ujian) && $ujian ? $ujian->nama_ujian : 'غير محدد' ?></td>
                    </tr>
                    <tr>
                        <th>عدد الاسئلة</th>
                        <td><?= isset($ujian) && $ujian ? $ujian->jumlah_soal : 'غير محدد' ?></td>
                    </tr>
                    <tr>
                        <th>الوقت</th>
                        <td><?= isset($ujian) && $ujian ? $ujian->waktu : 'غير محدد' ?> دقيقة</td>
                    </tr>
                    <tr>
                        <th>اخر ميعاد دخول للامتحان</th>
                        <td>
                            <?= isset($ujian) && $ujian && $ujian->terlambat ? date('d M Y', strtotime($ujian->terlambat)) : 'غير محدد' ?>
                            <?= isset($ujian) && $ujian && $ujian->terlambat ? date('h:i A', strtotime($ujian->terlambat)) : '' ?>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="col-sm-6">
                <div class="box box-solid">
                    <div class="box-body pb-0">
                        <p>اعاده الامتحان</p>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <?php
                            // عرض زر إعادة المحاولة فقط إذا كانت هناك محاولات متبقية
                            if (isset($adp_m_ujian) && isset($adp_adps)) {
                                $remaining_attempts = $adp_m_ujian - $adp_adps;
                                if ($remaining_attempts > 0) {
                                    echo '<button class="custom-btn" onclick="reExam()">
                                            <i class="fas fa-sync-alt"></i> إعادة الامتحان
                                            <span class="badge bg-green" style="margin-left: 10px;">' . $remaining_attempts . ' متبقية</span>
                                          </button>';
                                } else {
                                    echo '<button class="custom-btn" disabled style="background-color: #ccc; cursor: not-allowed;">
                                            <i class="fas fa-ban"></i> لا توجد محاولات متبقية
                                          </button>';
                                }
                            } else {
                                echo '<button class="custom-btn" disabled style="background-color: #ccc; cursor: not-allowed;">
                                        <i class="fas fa-exclamation-triangle"></i> خطأ في البيانات
                                      </button>';
                            }
                            ?>
                            <a href="<?= base_url('ujian/list') ?>" class="btn-back">
                                <i class="fa fa-arrow-left"></i> الرجوع لقائمة الامتحانات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function reExam() {
    const ujian_id = <?= isset($ujian) && $ujian ? $ujian->id_ujian : 0 ?>;
    const mahasiswa_id = <?= isset($hasil) && $hasil ? $hasil->mahasiswa_id : 0 ?>;
    const url = '<?= $_SERVER["PHP_SELF"]; ?>?ujian_id=' + ujian_id + '&mahasiswa_id=' + mahasiswa_id;
    window.location.href = url;
}
</script>

<?php
// Check if ujian_id and mahasiswa_id are set
if (isset($_GET['ujian_id']) && isset($_GET['mahasiswa_id'])) {
    $ujian_id = $_GET['ujian_id'];
    $mahasiswa_id = $_GET['mahasiswa_id'];

    // Database connection
    $conn = getMySQLiConnection();

    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    // Fetch data from h_ujian table
    $query = "SELECT * FROM h_ujian WHERE ujian_id = ? AND mahasiswa_id = ?";
    $stmt = $conn->prepare($query);
    if ($stmt) {
        $stmt->bind_param("ii", $ujian_id, $mahasiswa_id);
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        echo "<script>alert('خطأ في تحضير الاستعلام: " . $conn->error . "');</script>";
        return;
    }

    if ($result->num_rows > 0) {
        $data = $result->fetch_assoc();
        $list_soal = $data['list_soal'];
        $list_jawaban = $data['list_jawaban'];
        $jml_benar = $data['jml_benar'];
        $nilai = $data['nilai'];
        $nilai_bobot = $data['nilai_bobot'];

        // Check if record exists in adps table and fetch adp value
        $check_query = "SELECT adp FROM adps WHERE ujian_id = ? AND nim = ?";
        $check_stmt = $conn->prepare($check_query);
        if ($check_stmt) {
            $check_stmt->bind_param("is", $ujian_id, $mahasiswa_nim);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            $adp_adps = 0;
            if ($check_result->num_rows > 0) {
                $adp_adps = $check_result->fetch_assoc()['adp'];
            }
        } else {
            $adp_adps = 0;
            echo "<script>alert('خطأ في الاستعلام: " . $conn->error . "');</script>";
        }

        // Fetch adp limit from m_ujian table
        $m_ujian_query = "SELECT adp FROM m_ujian WHERE id_ujian = ?";
        $m_ujian_stmt = $conn->prepare($m_ujian_query);
        if ($m_ujian_stmt) {
            $m_ujian_stmt->bind_param("i", $ujian_id);
            $m_ujian_stmt->execute();
            $m_ujian_result = $m_ujian_stmt->get_result();
        } else {
            echo "<script>alert('خطأ في الاستعلام: " . $conn->error . "');</script>";
            return;
        }

        if ($m_ujian_result->num_rows > 0) {
            $adp_m_ujian = $m_ujian_result->fetch_assoc()['adp'];

            // Check if adp in adps is less than or equal to adp in m_ujian
            if ($adp_adps < $adp_m_ujian) {
                // If record exists in adps, update it; otherwise, insert a new record
                if ($check_result->num_rows > 0) {
                    $update_query = "UPDATE adps SET adp = adp + 1 WHERE ujian_id = ? AND nim = ?";
                    $update_stmt = $conn->prepare($update_query);
                    if ($update_stmt) {
                        $update_stmt->bind_param("is", $ujian_id, $mahasiswa_nim);
                        if (!$update_stmt->execute()) {
                            echo "<script>alert('Error updating adp: " . $update_stmt->error . "');</script>";
                        }
                    } else {
                        echo "<script>alert('خطأ في تحضير الاستعلام: " . $conn->error . "');</script>";
                    }
                } else {
                    $insert_adps_query = "INSERT INTO adps (ujian_id, nim, adp) VALUES (?, ?, 1)";
                    $insert_adps_stmt = $conn->prepare($insert_adps_query);
                    if ($insert_adps_stmt) {
                        $insert_adps_stmt->bind_param("is", $ujian_id, $mahasiswa_nim);
                        if (!$insert_adps_stmt->execute()) {
                            echo "<script>alert('Error inserting into adps: " . $insert_adps_stmt->error . "');</script>";
                        }
                    } else {
                        echo "<script>alert('خطأ في تحضير الاستعلام: " . $conn->error . "');</script>";
                    }
                }

                // Insert data into exam table
                $insert_query = "INSERT INTO exam (ujian_id, mahasiswa_id, list_soal, list_jawaban, jml_benar, nilai, nilai_bobot) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_query);
                if ($insert_stmt) {
                    $insert_stmt->bind_param("iisssdd", $ujian_id, $mahasiswa_id, $list_soal, $list_jawaban, $jml_benar, $nilai, $nilai_bobot);
                } else {
                    echo "<script>alert('خطأ في تحضير الاستعلام: " . $conn->error . "');</script>";
                    return;
                }

                if ($insert_stmt->execute()) {
                    // Delete the record from h_ujian table
                    $delete_query = "DELETE FROM h_ujian WHERE ujian_id = ? AND mahasiswa_id = ?";
                    $delete_stmt = $conn->prepare($delete_query);
                    if ($delete_stmt) {
                        $delete_stmt->bind_param("ii", $ujian_id, $mahasiswa_id);
                        if ($delete_stmt->execute()) {
                            // Redirect to the token page for the exam
                            $redirect_url = "/admin/ujian/token/" . $ujian_id;
                            header("Location: " . $redirect_url);
                            exit();
                        } else {
                            echo "<script>alert('Error deleting from h_ujian: " . $delete_stmt->error . "');</script>";
                        }
                    } else {
                        echo "<script>alert('خطأ في تحضير الاستعلام: " . $conn->error . "');</script>";
                    }
                } else {
                    echo "<script>alert('Error inserting into exam: " . $insert_stmt->error . "');</script>";
                }
            } else {
                // Display error message if adp exceeds limit
                echo '<div class="alert alert-danger" style="margin: 20px 0;">
                        <i class="fa fa-ban"></i>
                        <strong>عذراً!</strong> لقد تجاوزت الحد الأقصى لعدد المحاولات المسموح بها لهذا الامتحان.
                        <br><br>
                        <strong>تفاصيل المحاولات:</strong><br>
                        • عدد المحاولات الكلية: <span class="badge bg-blue">' . $adp_m_ujian . '</span><br>
                        • المحاولات المستخدمة: <span class="badge bg-red">' . $adp_adps . '</span><br>
                        • المحاولات المتبقية: <span class="badge bg-red">0</span>
                      </div>';
            }
        } else {
            echo "<script>alert('لم يتم العثور على البيانات في جدول m_ujian.');</script>";
        }
    } else {
        echo "<script>alert('لم يتم العثور على البيانات في جدول h_ujian.');</script>";
    }

    // Close statements and connection
    
    $conn->close();
}
?>
</body>
</html>
