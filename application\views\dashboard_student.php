<?php
// استخدام ملف قاعدة البيانات الموحد مرة واحدة فقط
// Use unified database connection file only once
if (!defined('DB_HOST')) {
    require_once 'db.php';
}
?>

<div class="row">
    <!-- معلومات الطالب -->
    <div class="col-md-4">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-user"></i> معلومات الحساب</h3>
            </div>
            <div class="box-body">
                <table class="table table-hover">
                    <tr>
                        <th><i class="fa fa-id-card"></i> رقم الطالب</th>
                        <td><?=$mahasiswa->nim?></td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-user"></i> الاسم</th>
                        <td><?=$mahasiswa->nama?></td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-envelope"></i> البريد الإلكتروني</th>
                        <td><?=$mahasiswa->email?></td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-graduation-cap"></i> القسم</th>
                        <td><?=$mahasiswa->nama_jurusan?></td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-users"></i> الفصل</th>
                        <td><?=$mahasiswa->nama_kelas?></td>
                    </tr>
                    <?php
                    // الحصول على حالة الحساب ومعلومات إضافية من جدول users
                    require_once 'db.php';
                    $account_status = 'غير محدد';
                    $status_color = '#6c757d';
                    $status_icon = 'fa-question-circle';
                    $last_login = 'غير محدد';
                    $created_on = 'غير محدد';

                    // إنشاء اتصال جديد
                    $conn = getMySQLiConnection();

                    if ($conn && !$conn->connect_error) {
                        $nim_escaped = $conn->real_escape_string($mahasiswa->nim);
                        $status_query = "SELECT active, last_login, FROM_UNIXTIME(created_on) as created_date FROM users WHERE username = '$nim_escaped'";
                        $status_result = $conn->query($status_query);

                        if ($status_result && $status_result->num_rows > 0) {
                            $status_row = $status_result->fetch_assoc();
                            if ($status_row['active'] == 1) {
                                $account_status = 'نشط';
                                $status_color = '#28a745';
                                $status_icon = 'fa-check-circle';
                            } else {
                                $account_status = 'معطل';
                                $status_color = '#dc3545';
                                $status_icon = 'fa-times-circle';
                            }

                            // آخر تسجيل دخول
                            if ($status_row['last_login'] && $status_row['last_login'] != '0') {
                                $last_login = date('Y-m-d H:i', $status_row['last_login']);
                            }

                            // تاريخ إنشاء الحساب
                            if ($status_row['created_date']) {
                                $created_on = $status_row['created_date'];
                            }
                        }
                    }
                    ?>
                    <tr>
                        <th><i class="fa fa-shield"></i> حالة الحساب</th>
                        <td>
                            <span style="color: <?=$status_color?>; font-weight: bold;">
                                <i class="fa <?=$status_icon?>"></i> <?=$account_status?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-clock-o"></i> آخر تسجيل دخول</th>
                        <td>
                            <span style="color: #17a2b8;">
                                <i class="fa fa-sign-in"></i> <?=$last_login?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th><i class="fa fa-calendar-plus-o"></i> تاريخ إنشاء الحساب</th>
                        <td>
                            <span style="color: #6c757d;">
                                <i class="fa fa-user-plus"></i> <?=$created_on?>
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- الامتحانات المتاحة -->
    <div class="col-md-4">
        <div class="box box-success">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-list"></i> الامتحانات المتاحة</h3>
            </div>
            <div class="box-body">
                <a href="<?=base_url('ujian')?>" class="btn btn-success btn-block">
                    <i class="fa fa-pencil"></i> دخول الامتحانات
                </a>
            </div>
        </div>
    </div>

    <!-- أدوات مساعدة -->
    <div class="col-md-4">
        <div class="box box-warning">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-tools"></i> أدوات مساعدة</h3>
            </div>
            <div class="box-body">
                <p class="text-muted">في حالة وجود مشكلة في حفظ درجات الامتحان، يمكنك استخدام الزر التالي لإعادة حساب النتائج:</p>
                <form method="post" action="errexam.php" style="margin-top: 10px;">
                    <input type="hidden" name="mp" value="<?php echo htmlspecialchars($mahasiswa->id_mahasiswa); ?>">
                    <button type="submit" class="btn btn-warning btn-block">
                        <i class="fa fa-refresh"></i> إعادة حساب النتائج
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نتائج الامتحانات -->
<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-chart-line"></i> نتائج الامتحانات</h3>
            </div>
            <div class="box-body">
                <?php
                // جلب نتائج الامتحانات
                try {
                    $conn = getMySQLiConnection();

                    if ($conn && !$conn->connect_error) {
                        $conn->set_charset("utf8");

                        // بيانات الطالب
                        if (isset($mahasiswa) && $mahasiswa) {
                        $mahasiswa_nama = $conn->real_escape_string($mahasiswa->nama);
                        $mahasiswa_nim = $conn->real_escape_string($mahasiswa->nim);
                        $mahasiswa_email = $conn->real_escape_string($mahasiswa->email);

                        // استعلام SQL لجلب النتائج
                        $sql = "SELECT m.nama_ujian, h.nilai, h.jml_benar, h.dikoreksi, h.selesai
                                FROM h_ujian h
                                JOIN m_ujian m ON h.ujian_id = m.id_ujian
                                JOIN mahasiswa mah ON h.mahasiswa_id = mah.id_mahasiswa
                                WHERE mah.nama = '$mahasiswa_nama' AND mah.nim = '$mahasiswa_nim' AND mah.email = '$mahasiswa_email'
                                ORDER BY h.selesai DESC";

                        $result = $conn->query($sql);

                        if ($result && $result->num_rows > 0) {
                            echo '<div class="table-responsive">';
                            echo '<table class="table table-striped table-hover">';
                            echo '<thead>';
                            echo '<tr>';
                            echo '<th><i class="fa fa-book"></i> اسم الامتحان</th>';
                            echo '<th><i class="fa fa-star"></i> درجتك</th>';
                            echo '<th><i class="fa fa-trophy"></i> الدرجة الكلية</th>';
                            echo '<th><i class="fa fa-percent"></i> النسبة المئوية</th>';
                            echo '<th><i class="fa fa-shield"></i> حالة الامتحان</th>';
                            echo '<th><i class="fa fa-calendar"></i> تاريخ الانتهاء</th>';
                            echo '</tr>';
                            echo '</thead>';
                            echo '<tbody>';
                            
                            while($row = $result->fetch_assoc()) {
                                $percentage = ($row["jml_benar"] != 0) ? round(($row["nilai"] / $row["jml_benar"]) * 100, 2) : 0;
                                $badge_class = $percentage >= 60 ? 'success' : 'danger';

                                // تحديد حالة الامتحان
                                $exam_status = '';
                                $status_badge = '';
                                if ($row["dikoreksi"] == 1) {
                                    $exam_status = 'مصحح';
                                    $status_badge = 'bg-green';
                                } elseif ($row["selesai"]) {
                                    $exam_status = 'مكتمل';
                                    $status_badge = 'bg-blue';
                                } else {
                                    $exam_status = 'غير مكتمل';
                                    $status_badge = 'bg-red';
                                }

                                echo '<tr>';
                                echo '<td>' . htmlspecialchars($row["nama_ujian"]) . '</td>';
                                echo '<td><span class="badge bg-blue">' . $row["nilai"] . '</span></td>';
                                echo '<td><span class="badge bg-gray">' . $row["jml_benar"] . '</span></td>';
                                echo '<td><span class="badge bg-' . $badge_class . '">' . $percentage . '%</span></td>';
                                echo '<td><span class="badge ' . $status_badge . '">' . $exam_status . '</span></td>';
                                echo '<td>' . ($row["selesai"] ? date('Y-m-d H:i', strtotime($row["selesai"])) : 'غير محدد') . '</td>';
                                echo '</tr>';
                            }
                            
                            echo '</tbody>';
                            echo '</table>';
                            echo '</div>';
                        } else {
                            echo '<div class="alert alert-info">';
                            echo '<i class="fa fa-info-circle"></i> لا توجد نتائج امتحانات حتى الآن';
                            echo '</div>';
                        }
                        } else {
                            echo '<div class="alert alert-warning">';
                            echo '<i class="fa fa-warning"></i> لا يمكن تحميل بيانات الطالب';
                            echo '</div>';
                        }

                        $conn->close();
                    } else {
                        echo '<div class="alert alert-danger">';
                        echo '<i class="fa fa-database"></i> فشل الاتصال بقاعدة البيانات';
                        echo '</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">';
                    echo '<i class="fa fa-exclamation-triangle"></i> حدث خطأ في تحميل البيانات';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>
