

<?php
// استخدام ملف قاعدة البيانات الموحد
// Use unified database connection file
if (!defined('DB_HOST')) {
    require_once 'db.php';
}

// استخدام الاتصال الموحد
// Use unified connection
$conn = getMySQLiConnection();
$conn->set_charset("utf8");

// الحصول على بيانات الطالب
$mahasiswa_nim = isset($mhs) && $mhs ? $conn->real_escape_string($mhs->nim) : '';
$mahasiswa_nam = isset($mhs) && $mhs ? $conn->real_escape_string($mhs->nama) : '';


// الحصول على User Agent
$user_agent = $_SERVER['HTTP_USER_AGENT'];

// استخراج اسم الجهاز من الـ user agent باستخدام تعبيرات منتظمة
function extractDeviceName($user_agent) {
    if (preg_match('/\((.*?)\)/', $user_agent, $matches)) {
        return $matches[1];
    }
    return 'Unknown Device';
}

$device_name = extractDeviceName($user_agent);

// التحقق من وجود الجهاز بالفعل في قاعدة البيانات بناءً على device_id
$sql_check_device_id = "SELECT * FROM devices WHERE device_id = '$mahasiswa_nim'";
$result_device_id = $conn->query($sql_check_device_id);

if ($result_device_id->num_rows > 0) {
    // الجهاز موجود بالفعل، التحقق من البيانات
    $row_device = $result_device_id->fetch_assoc();
    if ($row_device['user_agent'] != $device_name) {
        // توجيه إلى صفحة warning.php
        header("Location: warning.php");
        exit();
    }
} else {
    // الجهاز غير موجود، إضافة البيانات الجديدة
    $sql_insert = "INSERT INTO devices (name, device_id, user_agent) VALUES ('$mahasiswa_nam','$mahasiswa_nim', '$device_name')";
    $conn->query($sql_insert);
}

// إغلاق الاتصال
$conn->close();
?>

<div class="row">
	<div class="col-sm-3">
        <div class="alert bg-green">
            <h4>الفصل<i class="pull-right fa fa-building-o"></i></h4>
            <span class="d-block"> <?=isset($mhs) && $mhs ? $mhs->nama_kelas : 'غير محدد'?></span>
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-blue">
            <h4>القسم<i class="pull-right fa fa-graduation-cap"></i></h4>
            <span class="d-block"> <?=isset($mhs) && $mhs ? $mhs->nama_jurusan : 'غير محدد'?></span>
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-yellow">
            <h4>التاريخ<i class="pull-right fa fa-calendar"></i></h4>
            <span class="d-block"> <?=date('l, d M Y')?></span>                
        </div>
    </div>
    <div class="col-sm-3">
        <div class="alert bg-red">
            <h4>الساعة الان<i class="pull-right fa fa-clock-o"></i></h4>
            <span class="d-block"> <span class="live-clock"><?=date('H:i:s')?></span></span>                
        </div>
    </div>
    <div class="col-sm-12">
        <div class="box">
            <div class="box-header with-border">
                <h3 class="box-title"><?=$subjudul?></h3>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <div class="row">
                    <div class="col-sm-4">
                        <button type="button" onclick="reload_ajax()" class="btn btn-sm btn-flat bg-purple"><i class="fa fa-refresh"></i> اعادة ضبط</button>
                    </div>
                </div>
            </div>
            <div class="table-responsive px-4 pb-3" style="border: 0">
                <table id="ujian" class="w-100 table table-striped table-bordered table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الامتحان</th>
                        <th>المقرر</th>
						<th>المعلم</th>
                        <th>عدد الاسئلة</th>
                        <th>الوقت</th>
                        <th class="text-center">عدد المحاولات الكلية</th>
                        <th class="text-center">عدد المحاولات المستخدمة</th>
                        <th class="text-center">الدخول للامتحانات</th>
                        <th class="text-center">نتيجة اخر مره دخول للامتحان</th>
                        <th class="text-center">نتائج المحاولات السابقة</th>
                        <th class="text-center">الامتحان علي صورة كويز</th>


                    </tr>
                </thead>
                <!-- <tfoot>
                    <tr>
                        <th>#</th>
                        <th>Exam Name</th>
                        <th>Course</th>
						<th>Lecturer</th>
                        <th>Number of Questions</th>
                        <th>Time</th>
                        <th class="text-center">Action</th>
                    </tr>
                </tfoot> -->
                </table>
            </div>
        </div>
    </div>
</div>

<script src="<?=base_url()?>assets/dist/js/app/ujian/list.js"></script>
