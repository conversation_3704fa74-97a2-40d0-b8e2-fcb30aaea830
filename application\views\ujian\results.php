<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج الامتحان</title>
    <style>
     
        body {
            direction: ltr;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            position: relative;
            margin: 0;
        }

        .no-results-container {
            background: transparent;
            border-radius: 0;
            padding: 80px 20px;
            text-align: center;
            box-shadow: none;
            backdrop-filter: none;
            max-width: 100%;
            margin: 50px auto;
            animation: none;
            border: none;
        }

        .no-results-icon {
            width: 120px;
            height: 120px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 3rem;
            color: #6c757d;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid #e9ecef;
        }

        .no-results-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .no-results-message {
            font-size: 1.2rem;
            color: #5a6c7d;
            margin-bottom: 40px;
            line-height: 1.8;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .back-btn {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .back-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .back-btn:hover::before {
            left: 100%;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
            text-decoration: none;
            color: white;
            background: #0056b3;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .header {
            background: transparent;
            padding: 20px;
            color: #2c3e50;
            text-align: center;
            border-radius: 0;
            box-shadow: none;
            backdrop-filter: none;
            margin-bottom: 20px;
            max-width: 100%;
            margin-left: auto;
            margin-right: auto;
            border: none;
        }
        .header-content {
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        .header h1 {
            font-size: 2.5rem;
            margin: 0;
            font-weight: 700;
            text-shadow: none;
            letter-spacing: 1px;
            color: #2c3e50;
        }
       

        .page {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 25px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-width: 700px;
            margin: 20px auto;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .center-table {
            margin-top: 10px;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        h1, h2 {
            text-align: center;
            color: #333;
        }
        .arrow {
            font-size: 24px;
            margin: 0 10px;
            text-decoration: none;
            color: #333;
            transition: transform 0.3s ease;
            padding: 10px;
            border-radius: 50%;
            background-color: #f2f2f2;
        }
        .arrow:hover {
            transform: scale(1.2);
        }
        .arrow.prev {
            float: right;
        }
        .arrow.next {
            float: left;
        }
        .Btn {
            position: 100px;
            bottom: 20px;
            width: 100px;
            margin: 0 auto;
            left: 0;
            right: 0;
            border: none;
            padding: 0px 20px;
            background: linear-gradient(to right, #8be3fc, #576bff);
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            border-radius: 12px;
            box-shadow: 0 20px 30px -7px rgba(97, 118, 238, 0.5);
            transition-duration: 0.3s;
        }
        .Btn.prev {
            left: calc(50% - 60px);
        }
        .Btn.next {
            right: calc(50% - 60px);
        }
        .svg {
            width: 12px;
            position: absolute;
            right: 0;
            margin-right: 20px;
            fill: white;
            transition-duration: 0.3s;
        }
        .Btn:hover {
            color: transparent;
        }
        .Btn:hover svg {
            right: 43%;
            margin: 0;
            padding: 0;
            border: none;
            transition-duration: 0.3s;
        }
        .Btn:active {
            transform: translate(3px, 3px);
            transition-duration: 0.3s;
        }

    .question-block {
        margin-bottom: 20px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #f9f9f9;
    }

    .option {
        margin: 5px 0;
        display: flex;
        align-items: center;
    }

    .circle {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        background-color: #ddd;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10px;
        font-weight: bold;
    }

    .correct .circle {
        background-color: #28a745;
        color: #fff;
    }

    .wrong .circle {
        background-color: #dc3545;
        color: #fff;
    }

    .feedback {
        margin-left: 10px;
        font-weight: bold;
    }

    .correct-answer {
        margin-left: 10px;
        font-weight: bold;
        color: #007bff;
    }
            .question-block {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 10px;
            position: relative;
       } 
        .option.correct {
            background-color: #d4edda;
        }
        .option.wrong {
            background-color: #f8d7da;
        }
        .option {
            margin: 5px 0;
            display: flex;
            align-items: center;
        }
        .tools {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .tool {
            margin-left: 10px;
            cursor: pointer;
        }
        .highlight {
            background-color: yellow;
        }
        .note {
            margin-top: 10px;
            display: none;
        }
        .marked {
            border: 2px solid gold;
        }
        .note-container {
            margin-top: 10px;
        }
.question-list {
    position: relative;
    margin-top: 20px;
    text-align: center;
}

.toggle-list {
    position: absolute;
    top: -30px;
    right: 10px;
    cursor: pointer;
}

.toggle-list:before {
    content: '➕';
    font-size: 24px;
}

.question-list.active .toggle-list:before {
    content: '➖';
}

/* أنماط لتنسيق قائمة الأسئلة */
.question-list ul {
    padding: 0;
    list-style: none;
    display: flex; /* جعل العناصر في القائمة تظهر جنب بعضها */
    flex-wrap: wrap; /* إذا كانت العناصر كثيرة، سوف تنتقل إلى السطر التالي */
    justify-content: center; /* وضع العناصر في وسط الحاوية */
    transition: max-height 0.3s ease; /* إضافة تأثير سلس لإظهار/إخفاء القائمة */
    max-height: 0; /* افتراضيًا، إخفاء القائمة */
    overflow: hidden; /* إخفاء العناصر الزائدة */
}

.question-list.active ul {
    max-height: 200px; /* تحديد ارتفاع كافٍ لإظهار العناصر */
}

.question-list ul li {
    background-color: #3498db; /* لون الخلفية للدائرة */
    color: white; /* لون النص */
    width: 30px; /* عرض الدائرة */
    height: 30px; /* ارتفاع الدائرة */
    border-radius: 50%; /* تحويل العنصر إلى دائرة */
    display: flex; /* استخدام flexbox لمحاذاة النص في المركز */
    justify-content: center; /* محاذاة أفقية للمركز */
    align-items: center; /* محاذاة عمودية للمركز */
    margin: 5px; /* مساحة بين الدوائر */
    cursor: pointer; /* تغيير المؤشر عند التمرير فوق الدائرة */
    transition: transform 0.3s ease; /* تأثير عند التمرير */
}

.question-list ul li:hover {
    transform: scale(1.2); /* تكبير الدائرة عند التمرير */
}

.question-list ul li.active {
    background-color: #2c3e50; /* لون خلفية مغاير عندما تكون الدائرة نشطة */
}

    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <!-- رأس الصفحة -->
    <div class="header">
    <div class="header-content">
        <h1>SM A RT Examination System</h1>
        <!-- يمكنك إضافة مزيد من العناصر هنا، مثل الشعار أو الروابط -->
    </div>
</div>


    <!-- صفحة بيانات الطالب -->
    <div id="page1" class="page" style="display: block;">
<?php
// استخدام الاتصال الموحد بقاعدة البيانات
require_once APPPATH . '../db.php';

// الحصول على PDO connection
$pdo = getPDOConnection();

// استدعاء القيم من قاعدة البيانات
$results = [];
$mahasiswa_id = isset($mhs) && $mhs ? $mhs->id_mahasiswa : 0;
$ujian_id = isset($ujian) && $ujian ? $ujian->ujian_id : 0;

if ($pdo) {
    // استخدام PDO
    $query = "SELECT * FROM exam WHERE mahasiswa_id = ? AND ujian_id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$mahasiswa_id, $ujian_id]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    // استخدام mysqli
    $query = "SELECT * FROM exam WHERE mahasiswa_id = ? AND ujian_id = ?";
    $stmt = $conn->prepare($query);
    if ($stmt) {
        $stmt->bind_param("ii", $mahasiswa_id, $ujian_id);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $results[] = $row;
            }
        }
        $stmt->close();
    }
}

// التحقق من وجود بيانات
if (!$results) {
    echo '<div class="no-results-container">';
    echo '<div class="no-results-icon">📊</div>';
    echo '<h2 class="no-results-title">No Results Found</h2>';
    echo '<p class="no-results-message">.</p>';
    echo '<a href="' . base_url('ujian/list') . '" class="back-btn">← Back to Exam List</a>';
    echo '</div>';
    echo '</body></html>';
    return;
}

$current_data = isset($_GET['current_data']) ? $_GET['current_data'] : 0;
?>


<div class="center-table" style="margin-bottom: 20px;">
    <table>
        <tr>
            <th>رقم المحاولة</th>
            <th>النتائج</th>
            <th>النتيجة المعروضة</th>
        </tr>
        <?php foreach ($results as $index => $data): ?>
        <tr>
            <td><?php echo $index + 1; ?></td>
            <td>
                <form method="get" action="">
                    <input type="hidden" name="current_data" value="<?php echo $index; ?>">
                    <button type="submit">عرض</button>
                </form>
            </td>
            <td><?php echo ($current_data == $index) ? "✅" : ""; ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
</div>

<?php
// استدعاء الداتا الحالية
$current_data = $results[$current_data];
$jml_benar = $current_data['jml_benar'];
$ide = $current_data['id'];
$nilai = $current_data['nilai'];
?>

<h2>نتيجة الامتحان</h2>
<?php
// بيانات الاتصال بقاعدة البيانات
// استخدام ملف قاعدة البيانات الموحد
require_once 'db.php';

// إنشاء اتصال بقاعدة البيانات
$conn = getMySQLiConnection();

// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// الحصول على بيانات الطالب
$mahasiswa_nim = isset($mhs) && $mhs ? $conn->real_escape_string($mhs->nim) : '';
$mahasiswa_nam = isset($mhs) && $mhs ? $conn->real_escape_string($mhs->nama) : '';

// الحصول على User Agent
$user_agent = $_SERVER['HTTP_USER_AGENT'];

// استخراج اسم الجهاز من الـ user agent باستخدام تعبيرات منتظمة
function extractDeviceName($user_agent) {
    if (preg_match('/\((.*?)\)/', $user_agent, $matches)) {
        return $matches[1];
    }
    return 'Unknown Device';
}

$device_name = extractDeviceName($user_agent);

// التحقق من وجود الجهاز بالفعل في قاعدة البيانات بناءً على device_id
$sql_check_device_id = "SELECT * FROM devices WHERE device_id = '$mahasiswa_nim'";
$result_device_id = $conn->query($sql_check_device_id);

if ($result_device_id->num_rows > 0) {
    // الجهاز موجود بالفعل، التحقق من البيانات
    $row_device = $result_device_id->fetch_assoc();
    if ($row_device['user_agent'] != $device_name) {
        // توجيه إلى صفحة warning.php
        header("Location: warning.php");
        exit();
    }
} else {
    // الجهاز غير موجود، إضافة البيانات الجديدة
    $sql_insert = "INSERT INTO devices (name, device_id, user_agent) VALUES ('$mahasiswa_nam','$mahasiswa_nim', '$device_name')";
    $conn->query($sql_insert);
}

// إغلاق الاتصال
$conn->close();
?>

<div class="center-table" style="margin-bottom: 20px;">
    <table>
        <tr>
            <th>اسم الامتحان</th>
            <td><?php echo isset($ujian) && $ujian ? $ujian->nama_ujian : 'غير محدد'; ?></td>
        </tr>
        <tr>
            <th>الدرجة الكلية للامتحان</th>
            <td><?php echo $jml_benar; ?></td>
        </tr>
        <tr>
            <th>درجة الطالب</th>
            <td><?php echo $nilai; ?></td>
        </tr>
        <tr>
            <th>حالة الطالب</th>
            <td><?php echo ($jml_benar == 0 || $nilai < ($jml_benar * 0.5)) ? "راسب" : "ناجح"; ?></td>
        </tr>
        <tr>
            <th>تقدير الطالب</th>
            <td>
                <?php 
                $percentage = ($nilai / max(1, $jml_benar)) * 100; // التحقق من عدم وجود قيمة صفر لتجنب القسمة على صفر
                if ($percentage >= 85) {
                    echo "امتياز";
                } elseif ($percentage >= 75) {
                    echo "جيد جدا";
                } elseif ($percentage >= 65) {
                    echo "جيد";
                } elseif ($percentage >= 50) {
                    echo "مقبول";
                } else {
                    echo "راسب";
                }
                ?>
            </td>
        </tr>
    </table>
</div>

<button class="Btn next" onclick="nextPage()">Next
    <svg viewBox="0 0 320 2" class="svg">
        <path
            d="M52.5 440.6c-9.5 7.9-22.8 9.7-34.1 4.4S0 428.4 0 416V96C0 83.6 7.2 72.3 18.4 67s24.5-3.6 34.1 4.4l192 160L256 241V96c0-17.7 14.3-32 32-32s32 14.3 32 32V416c0 17.7-14.3 32-32 32s-32-14.3-32-32V271l-11.5 9.6-192 160z"
        ></path>
    </svg>
</button>
</div>


    <!-- صفحة بيانات الامتحان -->
    <!-- صفحة بيانات الامتحان -->
<div id="page2" class="page">
    <!-- مربع أرقام الأسئلة -->
<div class="question-list">
    <div class="toggle-list"></div>
    <span>قائمة الأسئلة</span> <!-- جملة قائمة الأسئلة -->
    <ul id="questionNumbers">
        <!-- سيتم ملء هذه القائمة ديناميكياً باستخدام جافاسكريبت -->
    </ul>
</div>



    <!-- مربع بيانات الأسئلة والإجابات -->
    <div class="center-table" style="margin-bottom: 20px;">
        

        <!-- مفاتيح اجابات الطالب -->
<?php
// استخدام ملف قاعدة البيانات الموحد
require_once 'db.php';

// الاتصال بقاعدة البيانات
$pdo = getPDOConnection();

// تعيين ترميز الاتصال بقاعدة البيانات إلى UTF-8
$pdo->exec("set names utf8");

// استعلام لاسترداد السؤال وإجابة الطالب
$query = "SELECT * FROM exam WHERE id = ? AND ujian_id = ?";
$stmt = $pdo->prepare($query);
$ujian_id = isset($ujian) && $ujian ? $ujian->ujian_id : 0;
$stmt->execute([$ide, $ujian_id]);
if (!isset($mhs) || !$mhs || !isset($ujian) || !$ujian) {
    header("Location: " . base_url('ujian'));
 
    exit;
} else {
    $row_number = 1;
    // عرض السؤال وإجابة الطالب
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo '<div class="question">';
        echo '<p><strong>إجابة الطالب:</strong></p>';
        $list_jawaban = explode(',', $row['list_jawaban']);
        foreach($list_jawaban as $jawaban) {
            $pieces = explode(':', $jawaban);
            // استعلام لاسترداد السؤال المطابق للقيمة في $pieces[0]
            $query_soal = "SELECT * FROM tb_soal WHERE id_soal = ?";
            $stmt_soal = $pdo->prepare($query_soal);
            $stmt_soal->execute([$pieces[0]]);
            $tb_soal = $stmt_soal->fetch(PDO::FETCH_ASSOC);

            // عرض السؤال والإجابات
            echo '<div class="question-block" id="question-' . $tb_soal['id_soal'] . '">';
            echo '<p><strong>' . $row_number++ . ') <span class="question-text">' . $tb_soal['soal'] . '</span></strong></p>';
            
            // أدوات
            echo '<div class="tools">';
            echo '<span class="tool highlight-tool" data-id="' . $tb_soal['id_soal'] . '">🖍️</span>';
            echo '<span class="tool note-tool" data-id="' . $tb_soal['id_soal'] . '">📝</span>';
            echo '<span class="tool mark-tool" data-id="' . $tb_soal['id_soal'] . '">⭐</span>';
            echo '</div>';

            // إضافة حقل لإدخال الملاحظات
            echo '<div class="note-container">';
            echo '<textarea class="note" placeholder="أضف ملاحظات هنا..." data-id="' . $tb_soal['id_soal'] . '"></textarea>';
            echo '</div>';

            // عرض الاختيارات
            $options = ['A', 'B', 'C', 'D'];
            foreach ($options as $option) {
                $option_value = $tb_soal['opsi_' . $option];
                $option_file = $tb_soal['file_' . $option];
                $correct_answer = $tb_soal['jawaban'];
                $student_answer = $pieces[1];

                $is_correct = strtolower($student_answer) == strtolower($correct_answer);
                $is_student_answer = strtolower($option) == strtolower($student_answer);

                $class = '';
                if ($is_student_answer) {
                    $class = $is_correct ? 'correct' : 'wrong';
                }

                // أضف هذا الشرط لإعطاء الصف correct للإجابة الصحيحة
                if (strtolower($option) == strtolower($correct_answer)) {
                    $class .= ' correct';
                }

                echo '<div class="option ' . $class . '">';
                echo '<span class="circle">' . $option . '</span> <span class="option-text" data-id="' . $tb_soal['id_soal'] . '-' . $option . '">' . $option_value . '</span>';
                echo '<span class="tool option-highlight-tool" data-id="' . $tb_soal['id_soal'] . '-' . $option . '">🖍️</span>';
                if ($option_file) {
                    echo '<img src="uploads/bank_soal/' . $option_file . '" />';
                }
                if ($is_student_answer) {
                    echo $is_correct ? '<span class="feedback">اجابتك صحيحة</span>' : '<span class="feedback">اجابتك خطأ</span>';
                }
                if (strtolower($option) == strtolower($correct_answer) && !$is_correct) {
                    echo '<span class="correct-answer">الاجابة الصحيحة</span>';
                }
                echo '</div>';
            
        }
        
    if (!empty($tb_soal['ex'])) {
                echo '<p class="explanation-label">توضيح الإجابة</p>';
                echo '<p class="explanation-value">' . $tb_soal['ex'] . '</p>';
            }

            echo '</div>'; // إغلاق question-block
        }
    }
}
?>


        </div>
        <button class="Btn prev" onclick="prevPage()">Previous 
            <svg viewBox="0 0 320 2" class="svg">
                <path
                    d="M52.5 440.6c-9.5 7.9-22.8 9.7-34.1 4.4S0 428.4 0 416V96C0 83.6 7.2 72.3 18.4 67s24.5-3.6 34.1 4.4l192 160L256 241V96c0-17.7 14.3-32 32-32s32 14.3 32 32V416c0 17.7-14.3 32-32 32s-32-14.3-32-32V271l-11.5 9.6-192 160z"
                ></path>
            </svg>
        </button>
    </div>
    <!-- إضافة أي جافاسكريبت هنا لتحقيق الانتقال بين الصفحات -->
    <script>
        var currentPage = 1;
        var totalPages = 4;

        function prevPage() {
            if (currentPage > 1) {
                document.getElementById("page" + currentPage).style.display = "none";
                currentPage--;
                document.getElementById("page" + currentPage).style.display = "block";
            }
        }

        function nextPage() {
            if (currentPage < totalPages) {
                document.getElementById("page" + currentPage).style.display = "none";
                currentPage++;
                document.getElementById("page" + currentPage).style.display = "block";
            }
        }
        document.addEventListener("DOMContentLoaded", function() {
    var questionBlocks = document.querySelectorAll('.question-block');
    var questionNumbersList = document.getElementById('questionNumbers');

    questionBlocks.forEach(function(block, index) {
        var questionNumberItem = document.createElement('li');
        questionNumberItem.textContent = index + 1;
        questionNumberItem.addEventListener('click', function() {
            block.scrollIntoView({ behavior: 'smooth' });
        });
        questionNumbersList.appendChild(questionNumberItem);
    });
});

document.addEventListener('DOMContentLoaded', function() {
    const toggleList = document.querySelector('.toggle-list');
    const questionList = document.querySelector('.question-list');

    toggleList.addEventListener('click', function() {
        questionList.classList.toggle('active');
    });
});


    </script>
<script>
    // تظليل النص
    document.querySelectorAll('.highlight-tool').forEach(tool => {
        tool.addEventListener('click', function() {
            const questionId = this.getAttribute('data-id');
            const questionBlock = document.getElementById('question-' + questionId);
            const questionText = questionBlock.querySelector('.question-text');
            questionText.classList.toggle('highlight');

            // حفظ التظليل في localStorage
            const isHighlighted = questionText.classList.contains('highlight');
            localStorage.setItem('highlight-' + questionId, isHighlighted);
        });
    });

    // تظليل الخيارات
    document.querySelectorAll('.option-highlight-tool').forEach(tool => {
        tool.addEventListener('click', function() {
            const optionId = this.getAttribute('data-id');
            const optionText = document.querySelector('[data-id="' + optionId + '"]');
            optionText.classList.toggle('highlight');

            // حفظ التظليل في localStorage
            const isHighlighted = optionText.classList.contains('highlight');
            localStorage.setItem('highlight-' + optionId, isHighlighted);
        });
    });

    // استعادة التظليل من localStorage
    document.querySelectorAll('.question-block').forEach(block => {
        const questionId = block.id.split('-')[1];
               const isHighlighted = localStorage.getItem('highlight-' + questionId) === 'true';
        if (isHighlighted) {
            block.querySelector('.question-text').classList.add('highlight');
        }

        block.querySelectorAll('.option-text').forEach(optionText => {
            const optionId = optionText.getAttribute('data-id');
            const isOptionHighlighted = localStorage.getItem('highlight-' + optionId) === 'true';
            if (isOptionHighlighted) {
                optionText.classList.add('highlight');
            }
        });

        // استعادة تمييز السؤال ككل
        const isMarked = localStorage.getItem('mark-' + questionId) === 'true';
        if (isMarked) {
            block.classList.add('marked');
        }

        // استعادة الملاحظات
        const note = localStorage.getItem('note-' + questionId);
        if (note) {
            const noteTextarea = block.querySelector('.note');
            noteTextarea.value = note;
            noteTextarea.style.display = 'block';
        }
    });

    // عرض وإخفاء الملاحظات عند النقر على علامة الملاحظات
    document.querySelectorAll('.note-tool').forEach(tool => {
        tool.addEventListener('click', function() {
            const questionId = this.getAttribute('data-id');
            const noteTextarea = document.querySelector('.note[data-id="' + questionId + '"]');
            noteTextarea.style.display = noteTextarea.style.display === 'block' ? 'none' : 'block';
        });
    });

    // تمييز السؤال ككل
    document.querySelectorAll('.mark-tool').forEach(tool => {
        tool.addEventListener('click', function() {
            const questionId = this.getAttribute('data-id');
            const questionBlock = document.getElementById('question-' + questionId);
            questionBlock.classList.toggle('marked');

            // حفظ التمييز في localStorage
            const isMarked = questionBlock.classList.contains('marked');
            localStorage.setItem('mark-' + questionId, isMarked);
        });
    });

    // حفظ الملاحظات
    document.querySelectorAll('.note').forEach(note => {
        note.addEventListener('input', function() {
            const questionId = this.getAttribute('data-id');
            const noteText = this.value;
            localStorage.setItem('note-' + questionId, noteText);
        });
    });
</script>

</body>
</html>
