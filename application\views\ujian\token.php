<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اخذ الامتحان</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
       
        .custom-btn {
            display: inline-block;
            padding: 8px 14px;
            background-color: #fff;
            color: black; /* تغيير لون النص إلى الأسود */
            border: 2px solid #4CAF50;
            border-radius: 5px;
            text-align: center;
            text-decoration: none;
            font-size: 10px;
          
            cursor: pointer;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        .custom-btn:hover {
            background-color: #45a049;
            border-color: #45a049;
        }
        .custom-btn i {
            margin-left: 5px;
        }
        .custom-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300%;
            height: 300%;
            background-color: #fff;
            border-radius: 50%;
            transition: all 0.3s ease;
            z-index: 0;
            transform: translate(-50%, -50%);
        }
        .custom-btn:hover::before {
            width: 0;
            height: 0;
        }
        .custom-btn span {
            position: relative;
            z-index: 1;
        }

        /* تنسيق إضافي لأزرار الرجوع */
        .btn-back {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-back:hover {
            background-color: #5a6268;
            border-color: #545b62;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
<div class="callout callout-info">
    <h4>تنبيهات الامتحان</h4>
    <p>رمز دخول الامتحان هو 1111</p>
    <?php
// استخدام ملف قاعدة البيانات الموحد
// Use unified database connection file
require_once 'db.php';

// استخدام الاتصال الموحد
// Use unified connection
$conn = getMySQLiConnection();
$conn->set_charset("utf8");

$mahasiswa_nim = $conn->real_escape_string($mhs->nim);
$mahasiswa_nam = $conn->real_escape_string($mhs->nama);


// الحصول على User Agent
$user_agent = $_SERVER['HTTP_USER_AGENT'];

// استخراج اسم الجهاز من الـ user agent باستخدام تعبيرات منتظمة
function extractDeviceName($user_agent) {
    if (preg_match('/\((.*?)\)/', $user_agent, $matches)) {
        return $matches[1];
    }
    return 'Unknown Device';
}

$device_name = extractDeviceName($user_agent);

// التحقق من وجود الجهاز بالفعل في قاعدة البيانات بناءً على device_id
$sql_check_device_id = "SELECT * FROM devices WHERE device_id = '$mahasiswa_nim'";
$result_device_id = $conn->query($sql_check_device_id);

if ($result_device_id->num_rows > 0) {
    // الجهاز موجود بالفعل، التحقق من البيانات
    $row_device = $result_device_id->fetch_assoc();
    if ($row_device['user_agent'] == $device_name) {
        echo "مسموح فقط بالدخول من هذا الجهاز";
    } else {
        // توجيه إلى صفحة warning.php
        header("Location: warning.php");
        exit();
    }
} else {
    // الجهاز غير موجود، إضافة البيانات الجديدة
    $sql_insert = "INSERT INTO devices (name, device_id, user_agent) VALUES ('$mahasiswa_nam','$mahasiswa_nim', '$device_name')";
    if ($conn->query($sql_insert) === TRUE) {
        echo "تمت اضافة هذا الجهاز بنجاح";
    } else {
        echo "Error: " . $sql_insert . "<br>" . $conn->error;
    }
}

// إغلاق الاتصال
$conn->close();
?>

    <p></p>
</div>
<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">Confirm Data</h3>
    </div>
    <div class="box-body">
        <span id="id_ujian" data-key="<?=$encrypted_id?>"></span>
        <div class="row">
            <div class="col-sm-6">
                <table class="table table-bordered">
                    <tr>
                        <th>الاسم</th>
                        <td><?=$mhs->nama?></td>
                    </tr>
                    <tr>
                        <th>المعلم</th>
                        <td><?=$ujian->nama_dosen?></td>
                    </tr>
                    <tr>
                        <th>الفصل\القسم</th>
                        <td><?=$mhs->nama_kelas?> / <?=$mhs->nama_jurusan?></td>
                    </tr>
                    <tr>
                        <th>اسم الامتحان</th>
                        <td><?=$ujian->nama_ujian?></td>
                    </tr>
                    <tr>
                        <th>عدد الاسئلة</th>
                        <td><?=$ujian->jumlah_soal?></td>
                    </tr>
                    <tr>
                        <th>الوقت</th>
                        <td><?= ($ujian->waktu ) ?> دقيقة</td>
                    </tr>
                    <tr>
                        <th>اخر ميعاد دخول للامتحان</th>
                        <td>
                            <?=date('d M Y', strtotime($ujian->terlambat))?> 
                            <?=date('h:i A', strtotime($ujian->terlambat))?>
                        </td>
                    </tr>
                    <tr>
                        <th style="vertical-align:middle">دخل رمز دخول الامتحان</th>
                        <td>
                            <input autocomplete="off" id="token" placeholder="ادخل الرمز" type="text" class="input-sm form-control">
                        </td>
                    </tr>
                </table>
            </div>
            <div class="col-sm-6">
                <div class="box box-solid">
                    <div class="box-body pb-0">
                
                        <?php
                        $base_url = base_url();
                        
                        $mulai = strtotime($ujian->tgl_mulai);
                        $terlambat = strtotime($ujian->terlambat);
                        $now = time();
                        if($mulai > $now) : 
                        ?>
                        	        <div class="callout callout-info">
                            <p> عندما يحين وقت بداية الامتحان سوف يظهر زر أبدأ الآن</p>
                        </div>
                        <div class="callout callout-success">
                            <strong><i class="fa fa-clock-o"></i> تاريخ بداية الامتحان</strong>
                            <br>
                            <span class="countdown" data-time="<?=date('Y-m-d H:i:s', strtotime($ujian->tgl_mulai))?>">00 Days, 00 Hours, 00 Minutes, 00 Seconds</strong><br/>
                        </div>
                        <div style="margin-bottom: 20px;">
                            <a href="<?= base_url('ujian/list') ?>" class="btn btn-back btn-lg">
                                <i class="fa fa-arrow-left"></i> الرجوع لقائمة الامتحانات
                            </a>
                        </div>
                        <?php elseif( $terlambat > $now ) : ?>
                        	        <div class="callout callout-info">
                            <p>هتدخل كود الامتحان المرسل لك عن طريق مسئول الامتحان ثم تضغط علي زر أبدأ الآن </p>
                        </div>
                        <div style="margin-bottom: 20px;">
                            <button id="btncek" data-id="<?=$ujian->id_ujian?>" class="btn btn-success btn-lg" style="margin-right: 10px;">
                                <i class="fa fa-pencil"></i> ابدا الان
                            </button>
                            <a href="<?= base_url('ujian/list') ?>" class="btn btn-back btn-lg">
                                <i class="fa fa-arrow-left"></i> الرجوع لقائمة الامتحانات
                            </a>
                        </div>
                        
                        <?php else : ?>
                            <?php
                            try {
                                // استخدام الاتصال الموحد
                                $pdo = getPDOConnection();

                                // استعلام لاسترداد السؤال وإجابة الطالب
                                if (isset($ujian) && isset($ujian->id_ujian)) {
                                    $query = "SELECT * FROM m_ujian WHERE id_ujian = ?";
                                    $stmt = $pdo->prepare($query);
                                    $stmt->execute([$ujian->id_ujian]);

                                    // عرض السؤال وإجابة الطالب
                                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                            ?>
                                        <a class="btn btn-xs btn-success custom-btn" href="<?= $base_url . 'hasilujian/cetak/' . $row['id_ujian'] ?>" target="_blank">
            <span>الحصول على نتيجة هذا الامتحان</span> <i class="fa fa-arrow-circle-right"></i>
        </a>
                            <?php
                                    }
                                } else {
                                    echo "معرف الامتحان غير متوفر";
                                }
                            } catch (PDOException $e) {
                                echo "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
                            } catch (Exception $e) {
                                echo "خطأ عام: " . $e->getMessage();
                            }
                            ?>
                            <div style="margin-top: 20px;">
                                <a href="<?= base_url('ujian/list') ?>" class="btn btn-back btn-lg">
                                    <i class="fa fa-arrow-left"></i> الرجوع لقائمة الامتحانات
                                </a>
                            </div>
                        <?php endif;?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    ajaxcsrf();

    $('#btncek').on('click', function(event) {
        event.preventDefault(); // منع إعادة تحميل الصفحة

        var token = $('#token').val();
        var idUjian = $(this).data('id');
        if (token === '') {
            Swal('فشل', 'لازم تكتب رمز دخول الامتحان', 'error');
        } else {
            var key = $('#id_ujian').data('key');
            $.ajax({
                url: base_url + 'ujian/cektoken/',
                type: 'POST',
                data: {
                    id_ujian: idUjian,
                    token: token
                },
                cache: false,
                success: function(result) {
                    Swal({
                        "type": result.status ? "تم بنجاح" : "فشل",
                        "title": result.status ? "تم بنجاح" : "فشل",
                        "text": result.status ? "رمز دخول صحيح" : "رمز دخول خطأ "
                    }).then((data) => {
                        if (result.status) {
                            location.href = base_url + 'ujian/?key=' + key;
                        }
                    });
                }
            });
        }
    });

    var time = $('.countdown');
    if (time.length) {
        countdown(time.data('time'));
    }
});
</script>
</body>
</html>
